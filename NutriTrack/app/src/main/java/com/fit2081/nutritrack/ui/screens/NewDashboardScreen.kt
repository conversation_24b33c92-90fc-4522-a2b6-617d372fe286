package com.fit2081.nutritrack.ui.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.fit2081.nutritrack.data.personaDetailsList
import com.fit2081.nutritrack.ui.components.NutriTrackNavigationBar
import com.fit2081.nutritrack.ui.components.ScoreCircle
import com.fit2081.nutritrack.ui.viewmodel.AuthViewModel
import com.fit2081.nutritrack.ui.viewmodel.DashboardState
import com.fit2081.nutritrack.ui.viewmodel.DashboardViewModel
import com.fit2081.nutritrack.utils.getFoodScoreDescription

@Composable
fun NewDashboardScreen(
    onEditQuestionnaire: () -> Unit,
    onNavigateToHome: () -> Unit,
    onNavigateToProfile: () -> Unit,
    onNavigateToInfo: () -> Unit,
    onNavigateToSettings: () -> Unit,
    authViewModel: AuthViewModel = hiltViewModel(),
    dashboardViewModel: DashboardViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val currentUserId by authViewModel.currentUserId.collectAsState()
    val dashboardState by dashboardViewModel.dashboardState.collectAsState()
    val foodScoreData by dashboardViewModel.foodScoreData.collectAsState()
    val questionnaireAnswers by dashboardViewModel.questionnaireAnswers.collectAsState()
    
    // Load dashboard data when user ID is available
    LaunchedEffect(currentUserId) {
        currentUserId?.let { userId ->
            dashboardViewModel.loadDashboardData(userId)
        }
    }
    
    Column(modifier = Modifier.fillMaxSize()) {
        when (dashboardState) {
            is DashboardState.Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            is DashboardState.Error -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = "加载失败",
                            style = MaterialTheme.typography.headlineSmall,
                            color = MaterialTheme.colorScheme.error
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = dashboardState.message,
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(onClick = {
                            currentUserId?.let { userId ->
                                dashboardViewModel.refreshData(userId)
                            }
                        }) {
                            Text("重试")
                        }
                    }
                }
            }
            is DashboardState.Success -> {
                LazyColumn(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    item {
                        // Welcome Section
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(12.dp),
                            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp)
                            ) {
                                Text(
                                    text = "欢迎回来！",
                                    fontSize = 24.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "用户ID: ${currentUserId ?: "未知"}",
                                    fontSize = 16.sp,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }

                    item {
                        // Food Score Section
                        foodScoreData?.let { scoreData ->
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                shape = RoundedCornerShape(12.dp),
                                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                            ) {
                                Column(
                                    modifier = Modifier.padding(16.dp),
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Text(
                                        text = "您的营养评分",
                                        fontSize = 20.sp,
                                        fontWeight = FontWeight.Bold,
                                        modifier = Modifier.padding(bottom = 16.dp)
                                    )
                                    
                                    ScoreCircle(
                                        score = scoreData.totalScore,
                                        maxScore = 100.0,
                                        size = 120.dp
                                    )
                                    
                                    Spacer(modifier = Modifier.height(16.dp))
                                    
                                    Text(
                                        text = getFoodScoreDescription(scoreData.totalScore),
                                        fontSize = 14.sp,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                                        modifier = Modifier.padding(horizontal = 8.dp)
                                    )
                                }
                            }
                        }
                    }

                    item {
                        // Persona Section
                        questionnaireAnswers?.let { answers ->
                            val persona = personaDetailsList.find { it.name == answers.persona }
                            persona?.let {
                                Card(
                                    modifier = Modifier.fillMaxWidth(),
                                    shape = RoundedCornerShape(12.dp),
                                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                                ) {
                                    Row(
                                        modifier = Modifier.padding(16.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Image(
                                            painter = painterResource(id = it.imageResId),
                                            contentDescription = it.name,
                                            modifier = Modifier
                                                .size(60.dp)
                                                .clip(CircleShape),
                                            contentScale = ContentScale.Crop
                                        )
                                        
                                        Spacer(modifier = Modifier.width(16.dp))
                                        
                                        Column(modifier = Modifier.weight(1f)) {
                                            Text(
                                                text = "您的饮食类型",
                                                fontSize = 14.sp,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                            Text(
                                                text = it.name,
                                                fontSize = 18.sp,
                                                fontWeight = FontWeight.Bold
                                            )
                                        }
                                        
                                        IconButton(onClick = onEditQuestionnaire) {
                                            Icon(
                                                imageVector = Icons.Default.Edit,
                                                contentDescription = "编辑问卷"
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }

                    item {
                        // Quick Stats Section
                        foodScoreData?.let { scoreData ->
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                shape = RoundedCornerShape(12.dp),
                                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                            ) {
                                Column(
                                    modifier = Modifier.padding(16.dp)
                                ) {
                                    Text(
                                        text = "营养细分",
                                        fontSize = 18.sp,
                                        fontWeight = FontWeight.Bold,
                                        modifier = Modifier.padding(bottom = 12.dp)
                                    )
                                    
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween
                                    ) {
                                        QuickStatItem("蔬菜", scoreData.vegetablesScore)
                                        QuickStatItem("水果", scoreData.fruitScore)
                                        QuickStatItem("谷物", scoreData.grainsScore)
                                    }
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween
                                    ) {
                                        QuickStatItem("乳制品", scoreData.dairyScore)
                                        QuickStatItem("水分", scoreData.waterScore)
                                        QuickStatItem("糖分", scoreData.sugarScore)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Navigation Bar
        NutriTrackNavigationBar(
            onNavigateToHome = onNavigateToHome,
            onNavigateToProfile = onNavigateToProfile,
            onNavigateToInfo = onNavigateToInfo,
            onNavigateToSettings = onNavigateToSettings,
            currentRoute = "dashboard"
        )
    }
}

@Composable
private fun QuickStatItem(label: String, score: Double) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .background(
                    color = when {
                        score >= 8 -> Color.Green.copy(alpha = 0.2f)
                        score >= 5 -> Color.Yellow.copy(alpha = 0.2f)
                        else -> Color.Red.copy(alpha = 0.2f)
                    },
                    shape = CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = String.format("%.1f", score),
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold
            )
        }
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = label,
            fontSize = 10.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
